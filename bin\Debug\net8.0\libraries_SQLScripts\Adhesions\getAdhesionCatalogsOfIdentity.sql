
/*


declare @pIdentiteId int
set @pIdentiteId = 473


*/

select aac.Adhesion_Catalog_ID, catalog_code, Catalog_Libelle, produit_id from Adhesion_Adherent_Catalog aac
inner join Adhesion_Catalog ac on ac.Adhesion_Catalog_ID = aac.Adhesion_Catalog_ID
inner join Adhesion_Adherent aa on aa.Adhesion_Adherent_ID = aac.Adhesion_Adherent_ID
where Adhesion_DateFin > GETDATE()
and Actif=1
and aa.Identite_id = @pIdentiteId