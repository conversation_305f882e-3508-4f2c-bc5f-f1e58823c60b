﻿
/* export les identites updatées depuis @dateparametre ou @lastexport */

declare @ClientTableLastExportDate datetime
declare @iCounter int

DECLARE @NumStructure varchar (4)


--declare @ppartnerId int = 123
--declare @pStartDate datetime = '01/01/1753 00:00:00'


set @NumStructure = convert(varchar,(select top 1 structure_id from structure))

--Verify param table
if  not exists(select so.name FROM sysobjects so where so.name = 'DataxplorerParamsApi')
	Begin
	    CREATE TABLE [dbo].DataxplorerParamsApi(
	         [ID] [decimal](18, 0) IDENTITY(1,1) NOT NULL,
			 partnerId int,
	         [Cle] [varchar](200) NOT NULL DEFAULT (''),
	         [Valeur] [varchar](200) NOT NULL DEFAULT (''),
	         [ID_DateTime] [datetime] NOT NULL	DEFAULT (getdate())
        ) ON [PRIMARY]
     End

--Example Param --> ClientTableLastExportDate = '01/01/2010 15:26:36'

if  exists(select so.name FROM sysobjects so where so.name = 'InfoComp')
	begin
	drop table InfoComp
	end
	
CREATE TABLE [dbo].[InfoComp](
	
	[ClientId]int NOT NULL DEFAULT(''),
	[InfoCompId]int NOT NULL DEFAULT(''),
	[InfoCompNom]varchar(50) NOT NULL DEFAULT(''),	
	[InfoCompGroupeId]varchar(50) NOT NULL DEFAULT(''),
	[InfoCompGroupeNom]varchar(50) NOT NULL DEFAULT(''),	
	[Valeur1]varchar(50) NOT NULL DEFAULT(''),
	[Valeur2]varchar(50) NOT NULL DEFAULT(''),
	[Valeur3]varchar(50) NOT NULL DEFAULT(''),
	[Valeur4]varchar(50) NOT NULL DEFAULT(''),	
	[DateCreation]datetime NOT NULL DEFAULT(''),	
	[DateModification]datetime NOT NULL,
	[TypeInfoComp]varchar(50) NOT NULL DEFAULT(''),
	[Supprime]varchar(50) NOT NULL DEFAULT(''),
	[Masquer]varchar(50) NOT NULL DEFAULT(''),
	[StructureId]int NOT NULL DEFAULT('')
		 
) ON [PRIMARY]


SELECT  @ClientTableLastExportDate =convert(datetime, valeur, 120) FROM DataxplorerParamsApi where cle LIKE 'InfoCompLastExportDate' AND partnerId = @ppartnerId
SET @ClientTableLastExportDate = Isnull(@ClientTableLastExportDate,convert(datetime, '01/01/1900 00:00:00', 120))

DECLARE @sincedate datetime
IF (@pStartDate = cast('1753-1-1' as datetime))
begin
	SET @sinceDate = @ClientTableLastExportDate	                
END
ELSE
BEGIN
	SET @sinceDate = @pStartDate	                
END

INSERT INTO InfoComp
	   SELECT 
	    iic.identite_id, 
	   CONVERT(varchar, iic.info_comp_id),
	   CONVERT(varchar, ic.libelle),
	   isnull(CONVERT(varchar, icg.groupe_id),''),
	   isnull(CONVERT(varchar, icg.libelle),''),
	   CONVERT(varchar, iic.valeur1),
	   CONVERT(varchar, iic.valeur2),
	   CONVERT(varchar, iic.valeur3),
	   CONVERT(varchar, iic.valeur4),
	  iic.datecreation,
	    iic.datemodification,
	   Case when ic.type_info_comp=0 Then 'libelle'
	   Else Case  When ic.type_info_comp=1 Then 'nombre'
	   Else Case  When ic.type_info_comp=2 Then 'date'
	   Else Case  When ic.type_info_comp=3 Then 'flag'
	   end end end end,
	   CONVERT(varchar, iic.supprimer),
	   CONVERT(varchar, ic.masquer),
	   @NumStructure                   
	   FROM identite_infos_comp iic
	   INNER JOIN info_comp ic on ic.info_comp_id=iic.info_comp_id
	   LEFT OUTER JOIN Info_comp_Grp icg on ic.groupe_id=icg.groupe_id
	   --Un Comment if modification only required
	   WHERE iic.datemodification > @sinceDate   
	   AND iic.datemodification < @pEndDate 
	   


--Un Comment if modification only required

SELECT  @iCounter =count(*)  FROM  DataxplorerParamsApi WHERE cle = 'InfoCompLastExportDate'  and partnerId = @ppartnerId
IF @iCounter>0
UPDATE DataxplorerParamsApi SET Valeur =getdate() ,ID_DateTime =getdate() WHERE  cle = 'InfoCompLastExportDate' and partnerId = @ppartnerId
ELSE
INSERT INTO DataxplorerParamsApi (partnerId, Cle,Valeur,ID_DateTime) values (@ppartnerId, 'InfoCompLastExportDate', getdate(),getdate())

SELECT * FROM InfoComp
---------------------------------------------------------------------------------------------

--Remove Temp Objects

IF EXISTS(select so.name FROM sysobjects so where so.name = 'InfoComp')
begin
	drop table InfoComp
end