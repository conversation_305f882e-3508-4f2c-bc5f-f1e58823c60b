﻿--DECLARE @pLangCode VARCHAR(2) = 'fr';
--DECLARE @pProduitId INT = 5650038;

-- Récupération de l'identifiant de la langue
DECLARE @idLangue INT = (
    SELECT langue_id 
    FROM langue 
    WHERE langue_code = @pLangCode
);

-- Récupération de l'indicateur "sans stock" pour le produit
DECLARE @produitSansStock INT = (
    SELECT sans_stock 
    FROM produit 
    WHERE produit_id = @pProduitId
);

-- Vérification si le produit est de type boutique
DECLARE @estProduitBoutique INT = (
    SELECT COUNT(*) 
    FROM Produit_Lien_Sous_Famille 
    WHERE produit_id = @pProduitId
);

-- Initialisation de la variable pour le montant du produit feedbook
DECLARE @feed_book_amount INT = 0;

-- Vérification de l'existence de la table feedbook_produit
IF OBJECT_ID('dbo.feedbook_produit', 'U') IS NOT NULL
BEGIN
    -- Calcul du montant du produit feedbook
    SET @feed_book_amount = (
        SELECT SUM((fgt.amount_1 * 100) * d.count) 
        FROM feedbook_produit fp
        INNER JOIN produit p ON p.produit_id = fp.produit_id
        INNER JOIN feedbooks_description d ON fp.produit_id = d.feedbook_produit_id
        INNER JOIN feedbook_token_produit_grilletarif fgt ON fgt.produit_id = d.feedbook_content_produit_id
        WHERE fgt.id = (
            SELECT MAX(id) 
            FROM feedbook_token_produit_grilletarif fgt2 
            WHERE fgt2.produit_id = fgt.produit_id
        )
        AND p.produit_id = @pProduitId
    );
END;



-- Variables pour la gestion multi-stock en cas de produit boutique
DECLARE @multiStockActive INT = 0;
DECLARE @stockMultiDepot INT = NULL; -- Stock restant spécifique pour multi-stock

IF @produitSansStock = 0 AND @estProduitBoutique = 1
BEGIN
    SET @multiStockActive = (
        SELECT COUNT(*) 
        FROM Produit_Droit_Boutique
        WHERE Droit_Boutique_Code = 'GESTION_MULTI_DEPOTS'
          AND Droit_Boutique_Valeur = 1
    );
    
    IF @multiStockActive = 1
    BEGIN
        -- Récupération du numéro de colonne du stock multi-dépôt
        DECLARE @restantCol INT = (
            SELECT CAST(
                RIGHT(
                    Droit_Boutique_Code, 
                    LEN(Droit_Boutique_Code) - CHARINDEX('_', Droit_Boutique_Code, CHARINDEX('_', Droit_Boutique_Code) + 1)
                ) AS INT
            )
            FROM Produit_Droit_Boutique
            WHERE Droit_Boutique_Code LIKE 'DEPOT_INTERNET_%'
              AND Droit_Boutique_Valeur = 1
        );

		
		IF @restantCol = 0
		BEGIN
			SELECT @stockMultiDepot = ps.restant
			FROM produit_stock ps
			WHERE ps.produit_id = @pProduitId;
		END
		ELSE
		BEGIN
		
			-- Requête dynamique paramétrée pour récupérer le stock multi-dépôt dans @stockMultiDepot
			DECLARE @sql NVARCHAR(MAX);
			SET @sql = N'
				SELECT @stockMultiDepotOUT = ps.restant_' + CAST(@restantCol AS NVARCHAR(10)) + '
				FROM produit_stock ps
				WHERE ps.produit_id = @pProduitId;';

			EXEC sp_executesql 
				@sql, 
				N'@pProduitId INT, @stockMultiDepotOUT INT OUTPUT',
				@pProduitId = @pProduitId,
				@stockMultiDepotOUT = @stockMultiDepot OUTPUT;

		END

    END;
END;



-- Construction d'un CTE pour unifier la sélection
;WITH CTEProduit AS (
    SELECT 
        p.produit_id,
        ISNULL(pf.Produit_Famille_ID, NULL) AS produit_famille_id,
        ISNULL(pf.produit_famille_nom, '') AS produit_famille_nom,
        pf.masquer AS produit_famille_masquer,
        ISNULL(psf.Produit_Sous_Famille_ID, NULL) AS produit_sous_famille_id,
        ISNULL(psf.produit_sous_famille_nom, '') AS produit_sous_famille_nom,
        psf.masquer AS produit_sous_famille_masquer,
        ISNULL(tp.produit_nom, p.produit_nom) AS produit_nom,
        ISNULL(tp.produit_descrip, p.produit_descrip) AS produit_description,
        ISNULL(tp.produit_code, p.produit_code) AS produit_reference,
        p_i.nb_min, 
        p_i.nb_max, 
        p_i.acces_autonome, 
        p_i.global_panier,
        CASE 
            WHEN ISNULL(gp.code, '') = 'CARNETJETONS'  THEN @feed_book_amount 
            ELSE ((ps.montant1 + ps.montant2) * 100) 
        END AS totalAmountInCent,
        ps.jauge,
        CASE 
            WHEN @produitSansStock = 1 THEN -1
            WHEN @produitSansStock = 0 AND @multiStockActive = 1 THEN ISNULL(@stockMultiDepot, ps.restant)
            ELSE ps.restant
        END AS restant,
        pdl.descriptif_long_text,
        p_i.site_vente,
        p_i.step,
        CASE WHEN p.unbilletparproduit = 'O' THEN 1 ELSE 0 END AS unbilletparproduit,
        CASE 
			WHEN p.groupe_id = 99 THEN 'BOUTIQUE' 
			ELSE ISNULL(gp.code, '')  END AS groupe_produit_code,
        Regroupement_couleur as regroupement_couleur,
        p.sans_stock,
        CASE WHEN p.type_montant = 0 THEN 0 
             WHEN p.type_montant = 1 THEN 1
       END AS is_montant_variable,
       Adhesion_Catalog_ID as adhesion_catalog_id
    FROM produit p
    LEFT JOIN GroupeProduit gp ON gp.ID = p.groupe_id
    LEFT JOIN Produit_Lien_Sous_Famille plsf ON plsf.produit_id = p.produit_id
    LEFT JOIN Produit_Famille pf ON plsf.Produit_Famille_ID = pf.Produit_Famille_ID
    LEFT JOIN Produit_Sous_Famille psf ON plsf.Produit_Sous_Famille_ID = psf.Produit_Sous_Famille_ID
    INNER JOIN produit_internet p_i ON p_i.produit_id = p.produit_id
    INNER JOIN produit_stock ps ON ps.produit_id = p.produit_id
        LEFT JOIN produit_descriptif_long pdl ON pdl.produit_ID = p.produit_ID
    LEFT JOIN traduction_produit tp ON tp.produit_id = p.produit_id AND tp.langue_id = @idLangue
    LEFT JOIN Adhesion_Catalog ac ON ac.Produit_ID = @pProduitId
    WHERE p.produit_id = @pProduitId
      AND GETDATE() BETWEEN p_i.date_deb_validite AND p_i.date_fin_validite
)
SELECT *
FROM CTEProduit;

