/**
 * Gestion de la synchronisation automatique des prestataires de paiement
 * entre la section PARAM et les sections complémentaires
 */

window.PrestataireSynchronizer = {
    
    /**
     * Initialise la synchronisation pour un champ PrestatairePaiement
     * @param {string} fieldId - ID du champ Select2 PrestatairePaiement
     * @param {object} dotNetReference - Référence vers l'objet .NET
     */
    initializeSync: function(fieldId, dotNetReference) {
        const select = $(`#${fieldId}`);
        
        if (select.length === 0) {
            console.warn(`Champ ${fieldId} non trouvé pour la synchronisation`);
            return;
        }

        // Écouter les changements sur le champ PrestatairePaiement
        select.on('change', function() {
            const selectedValues = $(this).val() || [];
            
            // Notifier le code C# du changement
            if (dotNetReference) {
                dotNetReference.invokeMethodAsync('OnPrestatairePaiementChanged', selectedValues);
            }
        });

        console.log(`Synchronisation initialisée pour ${fieldId}`);
    },

    /**
     * Met à jour la valeur du champ Select2 sans déclencher l'événement change
     * @param {string} fieldId - ID du champ Select2
     * @param {array} values - Nouvelles valeurs à sélectionner
     */
    updateFieldSilently: function(fieldId, values) {
        const select = $(`#${fieldId}`);
        
        if (select.length === 0) {
            console.warn(`Champ ${fieldId} non trouvé pour la mise à jour`);
            return;
        }

        // Désactiver temporairement l'événement change
        select.off('change.sync');
        
        // Mettre à jour la valeur
        select.val(values).trigger('change');
        
        // Réactiver l'événement change après un court délai
        setTimeout(() => {
            select.on('change.sync', function() {
                const selectedValues = $(this).val() || [];
                // Récupérer la référence .NET depuis l'attribut data
                const dotNetRef = select.data('dotnet-ref');
                if (dotNetRef) {
                    dotNetRef.invokeMethodAsync('OnPrestatairePaiementChanged', selectedValues);
                }
            });
        }, 100);
    },

    /**
     * Synchronise l'état des sections complémentaires avec le champ PrestatairePaiement
     * @param {array} selectedProviders - Liste des prestataires sélectionnés
     * @param {array} availableProviders - Liste de tous les prestataires disponibles
     */
    syncComplementarySections: function(selectedProviders, availableProviders) {
        availableProviders.forEach(provider => {
            const isSelected = selectedProviders.includes(provider);
            const sectionElement = $(`.section-switch[data-section="${provider}"]`);
            
            if (sectionElement.length > 0) {
                const switchInput = sectionElement.find('input[type="checkbox"]');
                
                // Mettre à jour l'état du switch sans déclencher l'événement
                if (switchInput.prop('checked') !== isSelected) {
                    switchInput.prop('checked', isSelected);
                    
                    // Déclencher visuellement le changement
                    if (isSelected) {
                        sectionElement.addClass('active');
                    } else {
                        sectionElement.removeClass('active');
                    }
                }
            }
        });
    },

    /**
     * Ajoute un prestataire à la liste sélectionnée
     * @param {string} fieldId - ID du champ Select2
     * @param {string} provider - Nom du prestataire à ajouter
     */
    addProvider: function(fieldId, provider) {
        const select = $(`#${fieldId}`);
        
        if (select.length === 0) return;
        
        const currentValues = select.val() || [];
        
        if (!currentValues.includes(provider)) {
            currentValues.push(provider);
            this.updateFieldSilently(fieldId, currentValues);
        }
    },

    /**
     * Supprime un prestataire de la liste sélectionnée
     * @param {string} fieldId - ID du champ Select2
     * @param {string} provider - Nom du prestataire à supprimer
     */
    removeProvider: function(fieldId, provider) {
        const select = $(`#${fieldId}`);
        
        if (select.length === 0) return;
        
        const currentValues = select.val() || [];
        const newValues = currentValues.filter(v => v !== provider);
        
        if (newValues.length !== currentValues.length) {
            this.updateFieldSilently(fieldId, newValues);
        }
    },

    /**
     * Obtient la liste actuelle des prestataires sélectionnés
     * @param {string} fieldId - ID du champ Select2
     * @returns {array} Liste des prestataires sélectionnés
     */
    getSelectedProviders: function(fieldId) {
        const select = $(`#${fieldId}`);
        return select.length > 0 ? (select.val() || []) : [];
    },

    /**
     * Vérifie si un prestataire est actuellement sélectionné
     * @param {string} fieldId - ID du champ Select2
     * @param {string} provider - Nom du prestataire à vérifier
     * @returns {boolean} True si le prestataire est sélectionné
     */
    isProviderSelected: function(fieldId, provider) {
        const selectedProviders = this.getSelectedProviders(fieldId);
        return selectedProviders.includes(provider);
    }
};

// Initialisation automatique quand le DOM est prêt
$(document).ready(function() {
    console.log('PrestataireSynchronizer initialisé');
    
    // Observer les changements dans le DOM pour les nouveaux champs Select2
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                    const select2Fields = $(node).find('select[id*="PrestatairePaiement"]');
                    select2Fields.each(function() {
                        const fieldId = $(this).attr('id');
                        console.log(`Nouveau champ PrestatairePaiement détecté: ${fieldId}`);
                        // L'initialisation sera faite par le code C#
                    });
                }
            });
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
