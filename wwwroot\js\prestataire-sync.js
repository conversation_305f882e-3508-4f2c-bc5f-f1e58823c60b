/**
 * Gestion de la synchronisation automatique des prestataires de paiement
 * entre la section PARAM et les sections complémentaires
 */

window.PrestataireSynchronizer = {
    
    /**
     * Initialise la synchronisation pour un champ PrestatairePaiement
     * @param {string} fieldId - ID du champ Select2 PrestatairePaiement
     * @param {object} dotNetReference - Référence vers l'objet .NET
     */
    initializeSync: function(fieldId, dotNetReference) {
        // Fonction pour tenter l'initialisation
        const attemptInit = () => {
            const select = $(`#${fieldId}`);

            if (select.length === 0) {
                console.warn(`Champ ${fieldId} non trouvé pour la synchronisation, nouvelle tentative dans 500ms`);
                return false;
            }

            // Supprimer les anciens événements pour éviter les doublons
            select.off('change.prestataire-sync');

            // Écouter les changements sur le champ PrestatairePaiement
            select.on('change.prestataire-sync', function() {
                const selectedValues = $(this).val() || [];

                // Notifier le code C# du changement
                if (dotNetReference) {
                    dotNetReference.invokeMethodAsync('OnPrestatairePaiementChanged', selectedValues);
                }
            });

            // Stocker la référence .NET pour usage ultérieur
            select.data('dotnet-ref', dotNetReference);

            console.log(`Synchronisation initialisée pour ${fieldId}`);
            return true;
        };

        // Tenter l'initialisation immédiatement
        if (!attemptInit()) {
            // Si échec, réessayer plusieurs fois avec des délais croissants
            let attempts = 0;
            const maxAttempts = 10;

            const retryInit = () => {
                attempts++;
                if (attempts > maxAttempts) {
                    console.error(`Impossible d'initialiser la synchronisation pour ${fieldId} après ${maxAttempts} tentatives`);
                    return;
                }

                setTimeout(() => {
                    if (!attemptInit()) {
                        retryInit();
                    }
                }, 500 * attempts); // Délai croissant
            };

            retryInit();
        }
    },

    /**
     * Met à jour la valeur du champ Select2 sans déclencher l'événement change
     * @param {string} fieldId - ID du champ Select2
     * @param {array} values - Nouvelles valeurs à sélectionner
     */
    updateFieldSilently: function(fieldId, values) {
        const select = $(`#${fieldId}`);

        if (select.length === 0) {
            console.warn(`Champ ${fieldId} non trouvé pour la mise à jour`);
            return;
        }

        // Désactiver temporairement l'événement change personnalisé
        select.off('change.prestataire-sync');

        // Mettre à jour la valeur
        select.val(values).trigger('change');

        // Réactiver l'événement change après un court délai
        setTimeout(() => {
            const dotNetRef = select.data('dotnet-ref');
            if (dotNetRef) {
                select.on('change.prestataire-sync', function() {
                    const selectedValues = $(this).val() || [];
                    dotNetRef.invokeMethodAsync('OnPrestatairePaiementChanged', selectedValues);
                });
            }
        }, 100);
    },

    /**
     * Synchronise l'état des sections complémentaires avec le champ PrestatairePaiement
     * @param {array} selectedProviders - Liste des prestataires sélectionnés
     * @param {array} availableProviders - Liste de tous les prestataires disponibles
     */
    syncComplementarySections: function(selectedProviders, availableProviders) {
        // Parcourir toutes les sections disponibles (détectées automatiquement)
        availableProviders.forEach(provider => {
            const isSelected = selectedProviders.includes(provider);
            const sectionElement = $(`.section-switch[data-section="${provider}"]`);

            if (sectionElement.length > 0) {
                const switchInput = sectionElement.find('input[type="checkbox"]');

                // Mettre à jour l'état du switch sans déclencher l'événement
                if (switchInput.prop('checked') !== isSelected) {
                    switchInput.prop('checked', isSelected);

                    // Déclencher visuellement le changement
                    if (isSelected) {
                        sectionElement.addClass('active');
                    } else {
                        sectionElement.removeClass('active');
                    }
                }
            }
        });
    },

    /**
     * Ajoute un prestataire à la liste sélectionnée
     * @param {string} fieldId - ID du champ Select2
     * @param {string} provider - Nom du prestataire à ajouter
     */
    addProvider: function(fieldId, provider) {
        const select = $(`#${fieldId}`);
        
        if (select.length === 0) return;
        
        const currentValues = select.val() || [];
        
        if (!currentValues.includes(provider)) {
            currentValues.push(provider);
            this.updateFieldSilently(fieldId, currentValues);
        }
    },

    /**
     * Supprime un prestataire de la liste sélectionnée
     * @param {string} fieldId - ID du champ Select2
     * @param {string} provider - Nom du prestataire à supprimer
     */
    removeProvider: function(fieldId, provider) {
        const select = $(`#${fieldId}`);
        
        if (select.length === 0) return;
        
        const currentValues = select.val() || [];
        const newValues = currentValues.filter(v => v !== provider);
        
        if (newValues.length !== currentValues.length) {
            this.updateFieldSilently(fieldId, newValues);
        }
    },

    /**
     * Obtient la liste actuelle des prestataires sélectionnés
     * @param {string} fieldId - ID du champ Select2
     * @returns {array} Liste des prestataires sélectionnés
     */
    getSelectedProviders: function(fieldId) {
        const select = $(`#${fieldId}`);
        return select.length > 0 ? (select.val() || []) : [];
    },

    /**
     * Vérifie si un prestataire est actuellement sélectionné
     * @param {string} fieldId - ID du champ Select2
     * @param {string} provider - Nom du prestataire à vérifier
     * @returns {boolean} True si le prestataire est sélectionné
     */
    isProviderSelected: function(fieldId, provider) {
        const selectedProviders = this.getSelectedProviders(fieldId);
        return selectedProviders.includes(provider);
    }
};

// Initialisation automatique quand le DOM est prêt
$(document).ready(function() {
    console.log('PrestataireSynchronizer initialisé');

    // Observer les changements dans le DOM pour les nouveaux champs Select2
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === 1) { // Element node
                    const select2Fields = $(node).find('select[id*="PrestatairePaiement"]');
                    select2Fields.each(function() {
                        const fieldId = $(this).attr('id');
                        console.log(`Nouveau champ PrestatairePaiement détecté: ${fieldId}`);

                        // Vérifier si le champ n'a pas déjà été initialisé
                        const select = $(this);
                        if (!select.data('prestataire-sync-initialized')) {
                            select.data('prestataire-sync-initialized', true);
                            console.log(`Marquage du champ ${fieldId} comme détecté`);
                        }
                    });
                }
            });
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});
