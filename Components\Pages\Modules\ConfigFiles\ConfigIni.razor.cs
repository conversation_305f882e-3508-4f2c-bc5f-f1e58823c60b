﻿using Core.Themis.Libraries.BLL.Services;
using Core.Themis.Libraries.Data.Entities.WsAdmin.Structure;
using Core.Themis.Libraries.Razor.Common.ViewModels;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Server.Circuits;
using Microsoft.JSInterop;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using ThemisSupportTools.Web.Components.Pages.Modules.Translations.Sections;

namespace ThemisSupportTools.Web.Components.Pages.Modules.ConfigFiles
{
    public partial class ConfigIni : IDisposable
    {
        const string MODULE_NAME = "config-ini";
        private TstRoleModel TstRole = new();
        private TstConfigIniViewModel TstConfigIniViewModel = new();
        private Modal modal = default!;
        private bool showConfigIniKeys = false;
        private string _currentUri;
        private bool _hasChanges = false;

        private List<ConfigIniSectionDTO> NonMandatorySections = new();

        List<ToastMessage> toastMessages = new List<ToastMessage>();
        ToastsPlacement toastsPlacement = ToastsPlacement.TopRight;
        [Inject] private CustomCircuitService CustomCircuitService { get; set; } = default!;

        TimeSpan TimeLeft;
        private ConfirmDialog dialog = new();
        public string FormClass { get; set; } = "needs-validation";
        private CancellationTokenSource _timerCts = new();
        private TimeSpan _initialTime;
        private bool _confirmDialogActive = false;

        [Inject] private NavigationManager NavigationManager { get; set; } = default!;
        [Inject] private IThemisSupportToolsManager ThemisSupportToolsManager { get; set; } = default!;
        [Inject] private ITstAccessService TstAccessService { get; set; } = default!;
        [Inject] private IJSRuntime _js { get; set; } = default!;
        [Inject] private IConfiguration Configuration { get; set; } = default!;
        [Inject] private NavigationManager Navigation { get; set; } = default!;
        [Inject] public required IStringLocalizer<Resource> Localizer { get; set; }
        [Inject] private IWsAdminStructuresManager WsAdminManager { get; set; } = default!;
        private List<ConfigIniSectionDTO> mandatorySections = new List<ConfigIniSectionDTO>();

        [Parameter]
        public int? StructureId { get; set; }

        private string structureName = string.Empty;

        public List<WsAdminStructureDTO> Structures { get; set; } = new();

        protected override void OnInitialized()
        {
            _currentUri = Navigation.Uri;
            Navigation.LocationChanged += OnLocationChanged;
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                if (StructureId is null)
                {
                    NavigationManager.NavigateTo(Navigation.BaseUri + "choose-structure/config-ini");
                }
                else
                {
                    structureName = TstAccessService.StructureName;

                    if (TstAccessService.IsGranted(MODULE_NAME))
                    {
                        TstRole = TstAccessService.GetRoleByModuleName(MODULE_NAME);

                        if (!TstRole.CanRead)
                        {
                            NavigationManager.NavigateTo("");
                        }

                        string currentUser = TstAccessService.GetUserName();
                        CustomCircuitService.CurrentUser = currentUser;
                        CustomCircuitService.StructureId = StructureId.Value;

                        TstConfigIniViewModel = await ThemisSupportToolsManager.GetConfigIniAsync(StructureId.Value, currentUser);

                        if (TstConfigIniViewModel.ConfigIniAllSections != null)
                        {
                            mandatorySections = TstConfigIniViewModel.ConfigIniAllSections
                                .Where(s => s.SectionFields.Any(f => f.IsMandatory))
                                .Select(s => new ConfigIniSectionDTO
                                {
                                    SectionName = s.SectionName,
                                    SectionFields = s.SectionFields
                                }).ToList();
                        }


                        if (!string.IsNullOrEmpty(TstConfigIniViewModel.Error))
                        {
                            var options = new ConfirmDialogOptions
                            {
                                YesButtonText = Localizer["change_structure_button_btnDanger"],
                                YesButtonColor = ButtonColor.Success,
                                NoButtonText = string.Empty,
                            };
                            await ShowConfirmationAsync(options, "Themis Support Tools", Localizer["file_in_use_message", TstConfigIniViewModel.UserNameWithTempFile.ToUpper(), StructureId.Value]);

                            NavigationManager.NavigateTo(Navigation.BaseUri + "choose-structure/config-ini");
                        }

                        if (TstConfigIniViewModel.ConfigIniSectionsOfUser != null)
                        {
                            TstConfigIniViewModel.ConfigIniSectionsOfUser.ForEach(x =>
                            {
                                x.SectionFields.ForEach(k =>
                                {
                                    if (k.IsMandatory)
                                        k.MandatoryMessage = Localizer[k.MandatoryMessage];
                                });
                            });
                        }
                    }
                    else
                    {
                        NavigationManager.NavigateTo("");
                    }
                }

                InitializeTimer();
                await StartTimer();

                // Initialiser la synchronisation JavaScript pour le champ PrestatairePaiement
                try
                {
                    var dotNetReference = DotNetObjectReference.Create(this);
                    await _js.InvokeVoidAsync("PrestataireSynchronizer.initializeSync",
                        "PARAM_PrestatairePaiement", dotNetReference);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Erreur lors de l'initialisation de la synchronisation: {ex.Message}");
                }
            }
        }


        private void InitializeTimer()
        {
            if (int.TryParse(Configuration["timerInMinute"], out int timerInMinutes))
            {
                _initialTime = new TimeSpan(0, timerInMinutes, 0);
            }
            else
            {
                _initialTime = new TimeSpan(0, 5, 0);
            }
            TimeLeft = _initialTime;
        }

        private async Task<bool> ShowConfirmationAsync(ConfirmDialogOptions dialogOptions, string title, string message)
        {
            var confirmation = await dialog.ShowAsync(
                title: title,
                message1: message,
                confirmDialogOptions: dialogOptions
            );

            return confirmation;
        }

        private async Task<bool> ShowConfirmationWithTimerAsync(string title, ConfirmDialogOptions options)
        {
            _confirmDialogActive = true;
            try
            {
                while (TimeLeft > TimeSpan.Zero)
                {
                    var message = string.Format(Localizer["time_warning_message"], TimeLeft.Minutes, TimeLeft.Seconds);
                    var confirmationTask = dialog.ShowAsync(
                        title: title,
                        message1: message,
                        confirmDialogOptions: options
                    );

                    await Task.Delay(millisecondsDelay: 1000);
                    TimeLeft = TimeLeft.Subtract(new TimeSpan(0, 0, 1));
                    StateHasChanged();

                    if (confirmationTask.IsCompleted)
                    {
                        bool result = await confirmationTask;
                        if (result)
                        {
                            // L'utilisateur a cliqué sur Oui
                            ResetTimer();
                        }
                        else
                        {
                            DeleteTmpFile();
                            NavigationManager.NavigateTo("choose-structure/config-ini");
                        }
                        return result;
                    }
                }

                // Si le temps est écoulé pendant l'affichage de la boîte de dialogue
                DeleteTmpFile();
                NavigationManager.NavigateTo("choose-structure/config-ini");
                return false;
            }
            finally
            {
                _confirmDialogActive = false;
            }
        }

        private void ScrollToTop()
        {
            _js.InvokeVoidAsync("scrollToTop");
        }

        private void OnLocationChanged(object sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
        {
            if (_currentUri != e.Location)
            {
                DeleteTmpFile();
                _currentUri = e.Location;
            }
        }

        // Méthode pour supprimer le fichier temporaire
        private void DeleteTmpFile()
        {
            if (StructureId.HasValue)
            {
                ThemisSupportToolsManager.DeleteConfigIniTempFile(StructureId.Value, TstAccessService.GetUserName());
                _hasChanges = false;
            }

            StopTimer();
        }

        public void Dispose()
        {
            Navigation.LocationChanged -= OnLocationChanged;
            StopTimer();

            CustomCircuitService.CurrentUser = null;
            CustomCircuitService.StructureId = 0;
        }

        /// <summary>
        /// Change la valeur d'un champ de section de configuration INI.
        /// Si le champ appartient à un groupe, les données de sélection sont mises à jour.
        /// Si le champ est connecté à un groupe, une fenêtre modale est affichée.
        /// </summary>
        /// <param name="field"></param>
        async Task ChangeValue(ConfigIniSectionFieldDTO field)
        {
            if (field is not null)
            {
                // Sauvegarder l'ancienne valeur pour la synchronisation
                var oldValue = field.FieldMultipleValue ?? Array.Empty<string>();

                if (!string.IsNullOrWhiteSpace(field.Groupe))
                {
                    ThemisSupportToolsManager.SetSelectDatas(TstConfigIniViewModel.ConfigIniSectionsOfUser);
                }

                if (!string.IsNullOrWhiteSpace(field.ConnectedToGroup))
                {
                    showConfigIniKeys = true;
                    await ShowModal();
                }

                // Vérifier si c'est le champ PrestatairePaiement dans la section PARAM
                if (field.FieldName.Equals("PrestatairePaiement", StringComparison.OrdinalIgnoreCase))
                {
                    var paramSection = TstConfigIniViewModel.ConfigIniSectionsOfUser?
                        .FirstOrDefault(s => s.SectionName.Equals("PARAM", StringComparison.OrdinalIgnoreCase));

                    if (paramSection != null && paramSection.SectionFields.Contains(field))
                    {
                        // Synchroniser les sections complémentaires
                        var newValue = field.FieldMultipleValue ?? Array.Empty<string>();
                        SynchronizeComplementarySections(newValue, oldValue);
                    }
                }

                _hasChanges = true;
                ResetTimer();
            }
        }

        private async Task TimerLoopAsync(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && TimeLeft > TimeSpan.Zero)
                {
                    // Vérifier si une boîte de dialogue est déjà active
                    if (_confirmDialogActive)
                    {
                        await Task.Delay(500, cancellationToken);
                        continue;
                    }

                    if (TimeLeft.TotalMinutes is > 1 and <= 2)
                    {
                        var options = new ConfirmDialogOptions
                        {
                            YesButtonText = Localizer["confirm_dialog_yes_button"],
                            YesButtonColor = ButtonColor.Success,
                            NoButtonText = Localizer["confirm_dialog_no_button"],
                            NoButtonColor = ButtonColor.Danger
                        };

                        await ShowConfirmationWithTimerAsync(
                            title: "Themis Support Tools",
                            options: options
                        );

                        return;
                    }

                    await Task.Delay(1000, cancellationToken);
                    TimeLeft = TimeLeft.Subtract(TimeSpan.FromSeconds(1));
                    StateHasChanged();
                }

                // Si le temps est écoulé, rediriger l'utilisateur
                if (TimeLeft <= TimeSpan.Zero && !cancellationToken.IsCancellationRequested)
                {
                    DeleteTmpFile();
                    NavigationManager.NavigateTo("choose-structure/config-ini");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur dans le timer : {ex.Message}");
            }
        }

        private void StopTimer()
        {
            try
            {
                _timerCts.Cancel();
                _timerCts.Dispose();
                _timerCts = new CancellationTokenSource();
            }
            catch
            {

            }
        }

        private void ResetTimer()
        {
            StopTimer();
            InitializeTimer();
            _ = StartTimer();
        }

        private async Task StartTimer()
        {
            StopTimer();
            _timerCts = new CancellationTokenSource();
            await TimerLoopAsync(_timerCts.Token);
        }

        /// <summary>
        /// Ajout et la suppression d'un prestateur de paiement
        /// </summary>
        /// <param name="section"></param>
        /// <returns></returns>
        async Task ChangeValueForSectionGroup(ConfigIniSectionDTO section)
        {
            if (section is not null)
            {
                if (section.EventToClick.Equals("Add", StringComparison.OrdinalIgnoreCase))
                {
                    section.IsChecked = true;

                    NonMandatorySections = GetNonMandatorySections(section.SectionName);

                    showConfigIniKeys = true;
                    await ShowModal();
                }
                else
                {
                    section.IsChecked = false;
                }

                SwitchChanged(section);
                ResetTimer();
            }
        }

        /// <summary>
        /// Suppression de la section dans la liste des parametres avancées
        /// </summary>
        /// <param name="section"></param>
        private void RemoveSection(ConfigIniSectionDTO section)
        {
            var sectionToRemove = TstConfigIniViewModel.ConfigIniSectionsOfUser!.FirstOrDefault(s => s.SectionName == section.SectionName);

            if (sectionToRemove is not null && !string.IsNullOrEmpty(sectionToRemove.SectionGroupe))
            {
                var fieldsConnectedToGroup = TstConfigIniViewModel.ConfigIniSectionsOfUser!.SelectMany(s => s.SectionFields.Where(f => f.ConnectedToGroup == sectionToRemove.SectionGroupe));

                foreach (var item in fieldsConnectedToGroup)
                {
                    item.FieldMultipleValue = item.FieldMultipleValue.Where(o => o != sectionToRemove.SectionName).ToArray();
                    item.FieldValue = string.Join(",", item.FieldMultipleValue);
                    item.IsModified = true;
                }
            }

            if (sectionToRemove != null)
            {
                TstConfigIniViewModel.ConfigIniSectionsOfUser!.Remove(sectionToRemove);
                sectionToRemove.IsChecked = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Met à jour l'état de la section spécifiée en fonction de son état actuel.
        /// Si la section est cochée, elle est ajoutée à la liste des sections de l'utilisateur.
        /// Si la section est décochée, elle est supprimée de cette liste.
        /// </summary>
        /// <param name="section"> </param>
        private void SwitchChanged(ConfigIniSectionDTO section)
        {
            var sectionNonMandatoryForPopup = GetNonMandatorySections().FirstOrDefault(s => s.SectionName == section.SectionName);
            if (sectionNonMandatoryForPopup is not null)
            {
                sectionNonMandatoryForPopup.IsChecked = section.IsChecked;
            }

            if (section.IsChecked)
            {
                if (!TstConfigIniViewModel.ConfigIniSectionsOfUser!.Any(s => s.SectionName == section.SectionName && s.ChildrenHasValue))
                {
                    TstConfigIniViewModel.ConfigIniSectionsOfUser!.Add(sectionNonMandatoryForPopup);
                    sectionNonMandatoryForPopup.SectionFields.ForEach(f => f.IsInCustmerXml = true);
                }

                // Synchroniser automatiquement avec le champ PrestatairePaiement dans PARAM
                SynchronizePrestatairePaiementField(section.SectionName, true);
            }
            else
            {
                RemoveSection(section);

                // Synchroniser automatiquement avec le champ PrestatairePaiement dans PARAM
                SynchronizePrestatairePaiementField(section.SectionName, false);
            }
            StateHasChanged();
        }

        private async Task AddSections()
        {
            showConfigIniKeys = false;
            NonMandatorySections = GetNonMandatorySections();

            await modal.ShowAsync();
        }

        private async Task ShowModal()
        {
            await modal.ShowAsync();
        }

        private async Task CloseModal()
        {
            await modal.HideAsync();
        }

        /// <summary>
        /// Synchronise automatiquement le champ PrestatairePaiement dans la section PARAM
        /// quand un prestataire est ajouté ou supprimé des sections complémentaires
        /// </summary>
        /// <param name="sectionName">Nom de la section prestataire</param>
        /// <param name="isAdded">True si ajouté, False si supprimé</param>
        private void SynchronizePrestatairePaiementField(string sectionName, bool isAdded)
        {
            // Vérifier si la section est un prestataire de paiement en vérifiant s'il existe dans les sections non obligatoires
            var isPaymentProvider = GetNonMandatorySections()
                .Any(s => s.SectionName.Equals(sectionName, StringComparison.OrdinalIgnoreCase));

            if (!isPaymentProvider)
                return;

            // Trouver la section PARAM dans les sections obligatoires
            var paramSection = TstConfigIniViewModel.ConfigIniSectionsOfUser?
                .FirstOrDefault(s => s.SectionName.Equals("PARAM", StringComparison.OrdinalIgnoreCase));

            if (paramSection == null)
                return;

            // Trouver le champ PrestatairePaiement dans la section PARAM
            var prestatairePaiementField = paramSection.SectionFields
                .FirstOrDefault(f => f.FieldName.Equals("PrestatairePaiement", StringComparison.OrdinalIgnoreCase));

            if (prestatairePaiementField == null)
                return;

            // Obtenir la liste actuelle des prestataires
            var currentProviders = string.IsNullOrEmpty(prestatairePaiementField.FieldValue)
                ? new List<string>()
                : prestatairePaiementField.FieldValue.Split(',')
                    .Select(p => p.Trim())
                    .Where(p => !string.IsNullOrEmpty(p))
                    .ToList();

            // Ajouter ou supprimer le prestataire
            if (isAdded)
            {
                // Ajouter seulement s'il n'existe pas déjà
                if (!currentProviders.Contains(sectionName, StringComparer.OrdinalIgnoreCase))
                {
                    currentProviders.Add(sectionName);
                }
            }
            else
            {
                // Supprimer le prestataire
                currentProviders.RemoveAll(p => p.Equals(sectionName, StringComparison.OrdinalIgnoreCase));
            }

            // Mettre à jour le champ
            prestatairePaiementField.FieldValue = string.Join(",", currentProviders);
            prestatairePaiementField.FieldMultipleValue = currentProviders.ToArray();
            prestatairePaiementField.IsModified = true;

            // Déclencher la mise à jour de l'interface utilisateur Select2
            InvokeAsync(async () =>
            {
                await _js.InvokeVoidAsync("PrestataireSynchronizer.updateFieldSilently",
                    "PARAM_PrestatairePaiement",
                    currentProviders.ToArray());
            });
        }

        /// <summary>
        /// Synchronise les sections complémentaires quand le champ PrestatairePaiement est modifié directement
        /// </summary>
        /// <param name="newProviders">Nouvelle liste des prestataires sélectionnés</param>
        /// <param name="oldProviders">Ancienne liste des prestataires</param>
        private void SynchronizeComplementarySections(string[] newProviders, string[] oldProviders)
        {
            // Obtenir tous les prestataires disponibles depuis les sections non obligatoires
            var availableProviders = GetNonMandatorySections()
                .Select(s => s.SectionName)
                .ToArray();

            // Trouver les prestataires ajoutés
            var addedProviders = newProviders?.Except(oldProviders ?? Array.Empty<string>(), StringComparer.OrdinalIgnoreCase) ?? Array.Empty<string>();

            // Trouver les prestataires supprimés
            var removedProviders = oldProviders?.Except(newProviders ?? Array.Empty<string>(), StringComparer.OrdinalIgnoreCase) ?? Array.Empty<string>();

            // Ajouter les nouvelles sections
            foreach (var provider in addedProviders)
            {
                // Vérifier si le provider existe dans les sections disponibles
                if (availableProviders.Contains(provider, StringComparer.OrdinalIgnoreCase))
                {
                    var section = GetNonMandatorySections().FirstOrDefault(s =>
                        s.SectionName.Equals(provider, StringComparison.OrdinalIgnoreCase));

                    if (section != null && !section.IsChecked)
                    {
                        section.IsChecked = true;
                        section.EventToClick = "Add";

                        // Ajouter la section sans déclencher la synchronisation inverse
                        if (!TstConfigIniViewModel.ConfigIniSectionsOfUser!.Any(s => s.SectionName == section.SectionName))
                        {
                            TstConfigIniViewModel.ConfigIniSectionsOfUser!.Add(section);
                            section.SectionFields.ForEach(f => f.IsInCustmerXml = true);
                        }
                    }
                }
            }

            // Supprimer les sections
            foreach (var provider in removedProviders)
            {
                // Vérifier si le provider existe dans les sections disponibles
                if (availableProviders.Contains(provider, StringComparer.OrdinalIgnoreCase))
                {
                    var section = TstConfigIniViewModel.ConfigIniSectionsOfUser?
                        .FirstOrDefault(s => s.SectionName.Equals(provider, StringComparison.OrdinalIgnoreCase));

                    if (section != null)
                    {
                        section.IsChecked = false;
                        TstConfigIniViewModel.ConfigIniSectionsOfUser!.Remove(section);

                        // Mettre à jour aussi dans les sections non obligatoires
                        var nonMandatorySection = GetNonMandatorySections()
                            .FirstOrDefault(s => s.SectionName.Equals(provider, StringComparison.OrdinalIgnoreCase));
                        if (nonMandatorySection != null)
                        {
                            nonMandatorySection.IsChecked = false;
                        }
                    }
                }
            }

            StateHasChanged();
        }

        private List<ConfigIniSectionDTO> GetNonMandatorySections(string? sectionName = null)
        {
            if (sectionName is not null)
            {
                return TstConfigIniViewModel.ConfigIniAllSections!
                    .Where(s => s.SectionName.Equals(sectionName, StringComparison.CurrentCultureIgnoreCase))
                    .ToList();
            }
            else
            {
             return TstConfigIniViewModel.ConfigIniAllSections!
                    .Where(s => s.SectionFields.All(f => !f.IsMandatory))
                    .Select(s => new ConfigIniSectionDTO
                    {
                        SectionName = s.SectionName,
                        SectionFields = s.SectionFields,
                        IsChecked = TstConfigIniViewModel.ConfigIniSectionsOfUser!.Any(u => u.SectionName == s.SectionName && u.ChildrenHasValue)
                    })
                    .ToList();

            }
        }

        private List<ConfigIniSectionDTO> GetNonMandatorySectionsOfUser()
        {
            if (TstConfigIniViewModel.ConfigIniSectionsOfUser is null)
            {
                return new List<ConfigIniSectionDTO>();
            }


            return TstConfigIniViewModel.ConfigIniSectionsOfUser
           .Where(s => s.SectionFields.All(f => !f.IsMandatory) && s.SectionFields.Any(f => f.IsInCustmerXml))
           .Select(s => new ConfigIniSectionDTO
           {
               SectionName = s.SectionName,
               SectionFields = s.SectionFields
           }).ToList();

        }


        // Méthode de validation des champs
        private bool ValidateForm(List<ConfigIniSectionDTO> sections)
        {
            bool allValid = true;

            foreach (var section in sections)
            {
                bool sectionHasError = false;

                foreach (var field in section.SectionFields)
                {
                    if (field.IsMandatory && string.IsNullOrWhiteSpace(field.FieldValue))
                    {
                        field.IsValid = false;
                        sectionHasError = true;
                        allValid = false;
                    }
                    else
                    {
                        field.IsValid = true;
                    }
                }

                if (sectionHasError)
                {
                    section.IsOpen = true;
                }
            }

            // Mise à jour de la liste des sections obligatoires
            mandatorySections = sections
                .Where(s => s.SectionFields.Any(f => f.IsMandatory))
                .OrderBy(s => s.SectionName)
                .ToList();

            FormClass = "was-validated";

            return allValid;
        }

        private void ShowValidationErrorToast(string message)
        {
            ToastMessage toastMessage = new ToastMessage
            {
                Type = ToastType.Danger,
                AutoHide = true,
                Title = Localizer["toast_message_title_form_validation"],
                Message = message
            };

            toastMessages.Add(toastMessage);
        }

        private async Task SaveFormAsync()
        {
            // Assurez-vous que toutes les sections obligatoires sont incluses
            var missingMandatorySections = TstConfigIniViewModel.ConfigIniAllSections?
                .Where(s => s.SectionFields.Any(f => f.IsMandatory))
                .Where(s => TstConfigIniViewModel.ConfigIniSectionsOfUser == null || !TstConfigIniViewModel.ConfigIniSectionsOfUser.Any(u => u.SectionName == s.SectionName))
                .OrderBy(s => s.SectionName)
                .ToList();

            // Ajouter les sections obligatoires manquantes
            if (missingMandatorySections != null)
            {
                foreach (var section in missingMandatorySections)
                {
                    TstConfigIniViewModel.ConfigIniSectionsOfUser?.Add(new ConfigIniSectionDTO
                    {
                        SectionName = section.SectionName,
                        SectionFields = section.SectionFields,
                        IsOpen = false
                    });
                }
            }

            if (TstConfigIniViewModel.ConfigIniSectionsOfUser != null && ValidateForm(TstConfigIniViewModel.ConfigIniSectionsOfUser))
            {
                try
                {
                    await ThemisSupportToolsManager.SauvegarderConfigIniAsync(StructureId.Value, TstConfigIniViewModel);
                    toastMessages.Add(new ToastMessage
                    {
                        Type = ToastType.Success,
                        AutoHide = true,
                        Title = Localizer["toast_message_title_success"],
                        Message = Localizer["toast_message_form_submitted"]
                    });
                    NavigationManager.NavigateTo(Navigation.BaseUri);
                }
                catch (Exception ex)
                {
                    toastMessages.Add(new ToastMessage
                    {
                        Type = ToastType.Danger,
                        AutoHide = true,
                        Title = Localizer["toast_message_title_error"],
                        Message = Localizer["toast_message_form_submission_failed", ex.Message]
                    });
                }
            }
            else
            {
                ShowValidationErrorToast(Localizer["toast_message_form_validation_failed"]);
                StateHasChanged();
            }
        }

        /// <summary>
        /// Méthode appelée depuis JavaScript quand le champ PrestatairePaiement change
        /// </summary>
        /// <param name="selectedProviders">Liste des prestataires sélectionnés</param>
        [JSInvokable]
        public void OnPrestatairePaiementChanged(string[] selectedProviders)
        {
            try
            {
                // Trouver le champ PrestatairePaiement dans la section PARAM
                var paramSection = TstConfigIniViewModel.ConfigIniSectionsOfUser?
                    .FirstOrDefault(s => s.SectionName.Equals("PARAM", StringComparison.OrdinalIgnoreCase));

                if (paramSection == null) return;

                var prestatairePaiementField = paramSection.SectionFields
                    .FirstOrDefault(f => f.FieldName.Equals("PrestatairePaiement", StringComparison.OrdinalIgnoreCase));

                if (prestatairePaiementField == null) return;

                // Sauvegarder l'ancienne valeur
                var oldProviders = prestatairePaiementField.FieldMultipleValue ?? Array.Empty<string>();

                // Mettre à jour le champ
                prestatairePaiementField.FieldMultipleValue = selectedProviders ?? Array.Empty<string>();
                prestatairePaiementField.FieldValue = string.Join(",", selectedProviders ?? Array.Empty<string>());
                prestatairePaiementField.IsModified = true;

                // Synchroniser les sections complémentaires
                SynchronizeComplementarySections(selectedProviders ?? Array.Empty<string>(), oldProviders);

                // Marquer qu'il y a des changements
                _hasChanges = true;
                ResetTimer();

                InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la synchronisation PrestatairePaiement: {ex.Message}");
            }
        }
    }
}