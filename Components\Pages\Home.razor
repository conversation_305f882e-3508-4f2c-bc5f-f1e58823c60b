@page "/"
@attribute [Authorize]
@attribute [StreamRendering]
@rendermode @(new InteractiveServerRenderMode(false))
@inject ITstAccessService TstAccessService
@inject IStringLocalizer<Resource> Localizer
@inject NavigationManager NavigationManager
@implements IDisposable

<PageTitle>Home</PageTitle>

<!-- Section des outils -->
<div class="container my-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-6 fw-bold text-primary mb-3">Outils de Gestion</h2>
            <p class="text-muted fs-5">@Localizer["home_outil_gesion_tst"]</p>
        </div>
    </div>

    <div class="row g-4">
        @foreach (var tool in Tools)
        {
            if (CanAccessTool(tool.Id))
            {
                <div class="col-lg-4 col-md-6">
                    <div class="dropdown position-relative">
                        <div class="card h-100 shadow border-0 tool-card"
                             style="transition: transform 0.3s, box-shadow 0.3s;"
                             @onclick="() => ToggleModules(tool.Id)"
                             @onmouseenter="() => ShowModules(tool.Id)"
                             @onmouseleave="() => StartHideTimer(tool.Id)">

                            <!-- Card principale de l'outil -->
                            <div class="card-body text-center p-4" style="cursor: pointer;">
                                <div class="rounded-circle p-3 d-inline-flex mb-3" style="background-color: @tool.Color;">
                                    <i class="@tool.Icon fs-1" style="color: white;"></i>
                                </div>
                                <h5 class="card-title fw-bold">@Localizer[tool.Name]</h5>
                                <p class="text-muted small">@Localizer[tool.Description]</p>
                                <small class="text-primary">
                                    <i class="bi bi-chevron-down"></i> @Localizer["common_voir_modules_label"]
                                </small>
                            </div>
                        </div>

                        <!-- Dropdown des modules -->
                        <div class="dropdown-menu @(visibleModules == tool.Id ? "show" : "") position-absolute shadow-lg border-0"
                             style="top: 100%; left: 0; right: 0; z-index: 1050; min-width: 100%;"
                             @onmouseenter="() => CancelHideTimer()"
                             @onmouseleave="() => HideModules()">
                            <div class="dropdown-header bg-light">
                                <strong>Modules disponibles</strong>
                            </div>
                            @foreach (var module in tool.Modules.Where(m => CanAccessModule(m.Name)))
                            {
                                <a href="@module.Name" class="dropdown-item py-2 px-3 d-flex align-items-center">
                                    <i class="@GetModuleIcon(module.Name) me-2 text-primary"></i>
                                    <span>@Localizer[module.Name]</span>
                                </a>
                            }
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</div>

<!-- Section statistiques -->
<div class="bg-light py-5 mt-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-primary">24/7</h3>
                    <p class="text-muted mb-0">@Localizer["stats_support_continu"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-success">+50%</h3>
                    <p class="text-muted mb-0">@Localizer["stats_productivite"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-info">100%</h3>
                    <p class="text-muted mb-0">@Localizer["stats_optimise"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-warning">∞</h3>
                    <p class="text-muted mb-0">@Localizer["stats_possibilites"]</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Section CTA -->
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <h3 class="fw-bold mb-3">@Localizer["home_worflow_questions"]</h3>
            <p class="text-muted mb-4">@Localizer["home_explorer_fonctionnalite"]</p>
            <div class="d-flex justify-content-center gap-3">
                <i class="bi bi-arrow-up text-primary fs-3"></i>
                <span class="text-muted">@Localizer["common_survolez_outils_pour_voir_modules"]</span>
            </div>
        </div>
    </div>
</div>

<style>
    .tool-card:hover {
        transform: scale(1.02);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .dropdown-menu {
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        background-color: white;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        color: #495057;
    }

    .dropdown-item:active {
        background-color: #e9ecef;
    }

    .dropdown-header {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        margin-bottom: 0;
        border-bottom: 1px solid #dee2e6;
    }
</style>

@code {
    private List<TstToolModel> Tools { get; set; } = new();
    private string? visibleModules = null;
    private Timer? hideTimer;

    protected override void OnInitialized()
    {
        Tools = TstAccessService.GetTools();
    }

    private bool CanAccessTool(string toolId)
    {
        return TstAccessService.CanAccessTool(toolId);
    }

    private bool CanAccessModule(string moduleName)
    {
        var role = TstAccessService.GetRoleByModuleName(moduleName);
        return role != null && role.CanRead;
    }

    private void ToggleModules(string toolId)
    {
        if (visibleModules == toolId)
        {
            visibleModules = null;
        }
        else
        {
            visibleModules = toolId;
        }
        StateHasChanged();
    }

    private void ShowModules(string toolId)
    {
        CancelHideTimer();
        visibleModules = toolId;
        StateHasChanged();
    }

    private void StartHideTimer(string toolId)
    {
        hideTimer?.Dispose();
        hideTimer = new Timer((_) =>
        {
            InvokeAsync(() =>
            {
                visibleModules = null;
                StateHasChanged();
            });
        }, null, 300, Timeout.Infinite); 
    }

    private void CancelHideTimer()
    {
        hideTimer?.Dispose();
        hideTimer = null;
    }

    private void HideModules()
    {
        StartHideTimer("");
    }

    public void Dispose()
    {
        hideTimer?.Dispose();
    }

    private string GetModuleIcon(string moduleName)  
    {  
       return moduleName switch  
              {  
                 "service-inclusion-exclusion" => "bi bi-sliders",  
                 "config-ini" => "bi bi-gear",  
                 "appsettings-plateforms" => "bi bi-layers",  
                 "translations-terms" => "bi bi-translate",  
                 "role-management" => "bi bi-people",  
                 "gestion-webtracing" => "bi bi-graph-up",  
                 "modifier-temps-panier" => "bi bi-clock",  
                 "gestion-coupons-promo" => "bi bi-tags",  
                 "transfere-pointage-photo" => "bi bi-camera",  
                 "gestion-maquette-abo-fermer" => "bi bi-box-arrow-in-down",  
                 "preparation-mise-vente" => "bi bi-shop",  
                 "partners" => "bi bi-handshake",  
                 "widget-waiting-list" => "bi bi-list-check",  
                 "widget-catalogue-offre" => "bi bi-card-list",  
                 _ => "bi bi-question-circle"  
              };  
    }
}