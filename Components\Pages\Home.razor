@page "/"
@attribute [Authorize]
@attribute [StreamRendering]
@rendermode @(new InteractiveServerRenderMode(false))
@inject ITstAccessService TstAccessService
@inject IStringLocalizer<Resource> Localizer
@inject NavigationManager NavigationManager
@implements IDisposable

<PageTitle>Home</PageTitle>

<!-- Section des outils -->
<div class="container my-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-6 fw-bold text-primary mb-3">Outils de Gestion</h2>
            <p class="text-muted fs-5">@Localizer["home_outil_gesion_tst"]</p>
        </div>
    </div>

    <div class="tools-grid">
        @foreach (var tool in Tools)
        {
            if (CanAccessTool(tool.Id))
            {
                <div class="tool-card-wrapper">
                    <!-- Card principale compacte -->
                    <div class="ultra-modern-card" @onclick="() => ToggleModules(tool.Id)">
                        <div class="card-icon" style="background: @tool.Color;">
                            <i class="@tool.Icon"></i>
                        </div>
                        <div class="card-content">
                            <h6 class="card-title">@Localizer[tool.Name]</h6>
                            <p class="card-subtitle">@Localizer[tool.Description]</p>
                            <div class="card-footer">
                                <span class="module-count">@GetModuleCount(tool) modules</span>
                                <i class="bi bi-chevron-down toggle-icon @(selectedTool == tool.Id ? "active" : "")"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Panel des modules (slide-out) -->
                    <div class="modules-panel @(selectedTool == tool.Id ? "active" : "")">
                        <div class="modules-header">
                            <span class="modules-title">Modules disponibles</span>
                            <button class="close-btn" @onclick="() => ToggleModules(tool.Id)" @onclick:stopPropagation="true">
                                <i class="bi bi-x"></i>
                            </button>
                        </div>
                        <div class="modules-list">
                            @foreach (var module in tool.Modules.Where(m => CanAccessModule(m.Name)))
                            {
                                <a href="@module.Name" class="module-link">
                                    <div class="module-icon-small">
                                        <i class="@GetModuleIcon(module.Name)"></i>
                                    </div>
                                    <span>@Localizer[module.Name]</span>
                                    <i class="bi bi-arrow-right-short"></i>
                                </a>
                            }
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</div>

<!-- Section statistiques -->
<div class="bg-light py-5 mt-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-primary">24/7</h3>
                    <p class="text-muted mb-0">@Localizer["stats_support_continu"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-success">+50%</h3>
                    <p class="text-muted mb-0">@Localizer["stats_productivite"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-info">100%</h3>
                    <p class="text-muted mb-0">@Localizer["stats_optimise"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-warning">∞</h3>
                    <p class="text-muted mb-0">@Localizer["stats_possibilites"]</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Section CTA -->
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <h3 class="fw-bold mb-3">@Localizer["home_worflow_questions"]</h3>
            <p class="text-muted mb-4">@Localizer["home_explorer_fonctionnalite"]</p>
            <div class="d-flex justify-content-center gap-3">
                <i class="bi bi-arrow-up text-primary fs-3"></i>
                <span class="text-muted">@Localizer["common_survolez_outils_pour_voir_modules"]</span>
            </div>
        </div>
    </div>
</div>

<style>
    .tools-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 24px;
        margin-bottom: 2rem;
    }

    .tool-card-wrapper {
        position: relative;
    }

    .ultra-modern-card {
        background: linear-gradient(145deg, #ffffff, #f8fafc);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 24px;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.08),
            0 1px 3px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.6);
        position: relative;
        overflow: hidden;
    }

    .ultra-modern-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .ultra-modern-card:hover::before {
        opacity: 1;
    }

    .ultra-modern-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow:
            0 20px 40px rgba(0, 0, 0, 0.15),
            0 8px 16px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }

    .card-icon {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
        position: relative;
    }

    .card-icon::after {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: 16px;
        background: linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
    }

    .card-icon i {
        font-size: 24px;
        color: white;
        z-index: 1;
        position: relative;
    }

    .card-content {
        flex: 1;
    }

    .card-title {
        font-size: 18px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 8px 0;
        letter-spacing: -0.025em;
    }

    .card-subtitle {
        font-size: 14px;
        color: #6b7280;
        margin: 0 0 16px 0;
        line-height: 1.5;
    }

    .card-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .module-count {
        font-size: 13px;
        color: #3b82f6;
        font-weight: 600;
        background: rgba(59, 130, 246, 0.1);
        padding: 4px 12px;
        border-radius: 20px;
    }

    .toggle-icon {
        font-size: 18px;
        color: #9ca3af;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .toggle-icon.active {
        transform: rotate(180deg);
        color: #3b82f6;
    }

    .modules-panel {
        position: absolute;
        top: 0;
        left: 100%;
        width: 100%;
        height: 100%;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        transform: translateX(20px);
        opacity: 0;
        visibility: hidden;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 10;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .modules-panel.active {
        transform: translateX(0);
        opacity: 1;
        visibility: visible;
    }

    .modules-header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid #f1f5f9;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .modules-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
    }

    .close-btn {
        width: 32px;
        height: 32px;
        border: none;
        background: #f8fafc;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .close-btn:hover {
        background: #e2e8f0;
        transform: scale(1.1);
    }

    .close-btn i {
        font-size: 16px;
        color: #64748b;
    }

    .modules-list {
        padding: 8px 16px 24px;
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .module-link {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-radius: 12px;
        text-decoration: none;
        color: #374151;
        transition: all 0.2s ease;
        gap: 12px;
    }

    .module-link:hover {
        background: #f8fafc;
        color: #1f2937;
        text-decoration: none;
        transform: translateX(4px);
    }

    .module-icon-small {
        width: 28px;
        height: 28px;
        background: #f1f5f9;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .module-icon-small i {
        font-size: 14px;
        color: #3b82f6;
    }

    .module-link span {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
    }

    .module-link i.bi-arrow-right-short {
        font-size: 18px;
        color: #cbd5e1;
        opacity: 0;
        transition: all 0.2s ease;
    }

    .module-link:hover i.bi-arrow-right-short {
        opacity: 1;
        color: #3b82f6;
    }

</style>

<!-- CSS Responsive séparé -->
<style>
    @@media (max-width: 768px) {
        .tools-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .modules-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            width: 90vw;
            max-width: 400px;
            height: auto;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modules-panel.active {
            transform: translate(-50%, -50%) scale(1);
        }
    }
</style>

<!-- CSS avec keyframes séparé -->
<style>
    @@keyframes slideInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

@code {
    private List<TstToolModel> Tools { get; set; } = new();
    private string? selectedTool = null;

    protected override void OnInitialized()
    {
        Tools = TstAccessService.GetTools();
    }

    private bool CanAccessTool(string toolId)
    {
        return TstAccessService.CanAccessTool(toolId);
    }

    private bool CanAccessModule(string moduleName)
    {
        var role = TstAccessService.GetRoleByModuleName(moduleName);
        return role != null && role.CanRead;
    }

    private void ToggleModules(string toolId)
    {
        if (selectedTool == toolId)
        {
            selectedTool = null;
        }
        else
        {
            selectedTool = toolId;
        }
        StateHasChanged();
    }

    private int GetModuleCount(TstToolModel tool)
    {
        return tool.Modules?.Where(m => CanAccessModule(m.Name)).Count() ?? 0;
    }

    public void Dispose()
    {
        // Méthode requise par IDisposable - pas de ressources à libérer dans ce nouveau design
    }

    private string GetModuleIcon(string moduleName)  
    {  
       return moduleName switch  
              {  
                 "service-inclusion-exclusion" => "bi bi-sliders",  
                 "config-ini" => "bi bi-gear",  
                 "appsettings-plateforms" => "bi bi-layers",  
                 "translations-terms" => "bi bi-translate",  
                 "role-management" => "bi bi-people",  
                 "gestion-webtracing" => "bi bi-graph-up",  
                 "modifier-temps-panier" => "bi bi-clock",  
                 "gestion-coupons-promo" => "bi bi-tags",  
                 "transfere-pointage-photo" => "bi bi-camera",  
                 "gestion-maquette-abo-fermer" => "bi bi-box-arrow-in-down",  
                 "preparation-mise-vente" => "bi bi-shop",  
                 "partners" => "bi bi-handshake",  
                 "widget-waiting-list" => "bi bi-list-check",  
                 "widget-catalogue-offre" => "bi bi-card-list",  
                 _ => "bi bi-question-circle"  
              };  
    }
}