@page "/"
@attribute [Authorize]
@attribute [StreamRendering]
@rendermode @(new InteractiveServerRenderMode(false))
@inject ITstAccessService TstAccessService
@inject IStringLocalizer<Resource> Localizer
@inject NavigationManager NavigationManager
@implements IDisposable

<PageTitle>Home</PageTitle>

<!-- Section des outils -->
<div class="container my-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-6 fw-bold text-primary mb-3">Outils de Gestion</h2>
            <p class="text-muted fs-5">@Localizer["home_outil_gesion_tst"]</p>
        </div>
    </div>

    <div class="tools-tree-container">
        @foreach (var tool in Tools)
        {
            if (CanAccessTool(tool.Id))
            {
                <div class="tree-node"
                     @onmouseenter="() => ShowBubbles(tool.Id)"
                     @onmouseleave="() => HideBubbles(tool.Id)">

                    <!-- <PERSON>te principale (tronc de l'arbre) -->
                    <div class="main-bubble" style="--bubble-color: @tool.Color;">
                        <div class="bubble-icon">
                            <i class="@tool.Icon"></i>
                        </div>
                        <div class="bubble-content">
                            <h6 class="bubble-title">@Localizer[tool.Name]</h6>
                            <p class="bubble-description">@Localizer[tool.Description]</p>
                            <span class="bubble-count">@GetModuleCount(tool) modules</span>
                        </div>
                        <div class="bubble-glow"></div>
                    </div>

                    <!-- Menu déroulant des modules -->
                    <div class="modules-tree @(hoveredTool == tool.Id ? "visible" : "")">
                        <div class="modules-list">
                            @foreach (var module in tool.Modules.Where(m => CanAccessModule(m.Name)))
                            {
                                <a href="@module.Name" class="module-bubble">
                                    <div class="module-bubble-icon">
                                        <i class="@GetModuleIcon(module.Name)"></i>
                                    </div>
                                    <span class="module-bubble-text">@Localizer[module.Name]</span>
                                    <i class="bi bi-arrow-right-short module-arrow"></i>
                                </a>
                            }
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</div>

<!-- Section statistiques -->
<div class="bg-light py-5 mt-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-primary">24/7</h3>
                    <p class="text-muted mb-0">@Localizer["stats_support_continu"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-success">+50%</h3>
                    <p class="text-muted mb-0">@Localizer["stats_productivite"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-info">100%</h3>
                    <p class="text-muted mb-0">@Localizer["stats_optimise"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-warning">∞</h3>
                    <p class="text-muted mb-0">@Localizer["stats_possibilites"]</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Section CTA -->
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <h3 class="fw-bold mb-3">@Localizer["home_worflow_questions"]</h3>
            <p class="text-muted mb-4">@Localizer["home_explorer_fonctionnalite"]</p>
            <div class="d-flex justify-content-center gap-3">
                <i class="bi bi-arrow-up text-primary fs-3"></i>
                <span class="text-muted">@Localizer["common_survolez_outils_pour_voir_modules"]</span>
            </div>
        </div>
    </div>
</div>

<style>
    .tools-tree-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .tree-node {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 200px;
        padding: 20px;
    }

    .main-bubble {
        position: relative;
        width: 100%;
        max-width: 280px;
        height: 140px;
        background: linear-gradient(135deg, #ffffff, #f8fafc);
        border-radius: 20px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        box-shadow:
            0 4px 20px rgba(0, 0, 0, 0.08),
            0 2px 8px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        border: 2px solid var(--bubble-color);
        display: flex;
        align-items: center;
        gap: 16px;
        z-index: 10;
    }

    .main-bubble:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.15),
            0 4px 16px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.9);
    }

    .bubble-icon {
        width: 64px;
        height: 64px;
        background: var(--bubble-color);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        flex-shrink: 0;
    }

    .bubble-icon i {
        font-size: 28px;
        color: white;
    }

    .bubble-content {
        flex: 1;
        min-width: 0;
    }

    .bubble-title {
        font-size: 18px;
        font-weight: 700;
        color: #1f2937;
        margin: 0 0 6px 0;
        letter-spacing: -0.025em;
    }

    .bubble-description {
        font-size: 13px;
        color: #6b7280;
        margin: 0 0 8px 0;
        line-height: 1.4;
    }

    .bubble-count {
        font-size: 12px;
        color: var(--bubble-color);
        font-weight: 600;
        background: rgba(59, 130, 246, 0.1);
        padding: 4px 10px;
        border-radius: 12px;
        display: inline-block;
    }

    .bubble-glow {
        position: absolute;
        inset: -4px;
        background: var(--bubble-color);
        border-radius: 28px;
        opacity: 0;
        filter: blur(20px);
        transition: opacity 0.4s ease;
        z-index: -1;
    }

    .main-bubble:hover .bubble-glow {
        opacity: 0.3;
    }

    .modules-tree {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        width: 400px;
        max-width: 90vw;
        background: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        border: 1px solid #e5e7eb;
        padding: 16px;
        opacity: 0;
        visibility: hidden;
        transform: translateX(-50%) translateY(-10px);
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        z-index: 20;
        margin-top: 12px;
    }

    .modules-tree.visible {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
    }

    .modules-tree::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 16px;
        height: 16px;
        background: white;
        border: 1px solid #e5e7eb;
        border-bottom: none;
        border-right: none;
        transform: translateX(-50%) rotate(45deg);
    }

    .modules-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .module-bubble {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-radius: 12px;
        text-decoration: none;
        color: #374151;
        transition: all 0.2s ease;
        gap: 12px;
        border: 1px solid transparent;
    }

    .module-bubble:hover {
        background: #f8fafc;
        color: #1f2937;
        text-decoration: none;
        transform: translateX(4px);
        border-color: #e2e8f0;
    }

    .module-bubble-icon {
        width: 32px;
        height: 32px;
        background: #f1f5f9;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .module-bubble-icon i {
        font-size: 16px;
        color: #3b82f6;
    }

    .module-bubble-text {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        line-height: 1.3;
    }

    .module-arrow {
        font-size: 16px;
        color: #cbd5e1;
        opacity: 0;
        transition: all 0.2s ease;
    }

    .module-bubble:hover .module-arrow {
        opacity: 1;
        color: #3b82f6;
    }

</style>

<!-- CSS Responsive séparé -->
<style>
    @@media (max-width: 768px) {
        .tools-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .modules-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            width: 90vw;
            max-width: 400px;
            height: auto;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modules-panel.active {
            transform: translate(-50%, -50%) scale(1);
        }
    }
</style>

<!-- CSS avec keyframes séparé -->
<style>
    @@keyframes slideInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

@code {
    private List<TstToolModel> Tools { get; set; } = new();
    private string? hoveredTool = null;

    protected override void OnInitialized()
    {
        Tools = TstAccessService.GetTools();
    }

    private bool CanAccessTool(string toolId)
    {
        return TstAccessService.CanAccessTool(toolId);
    }

    private bool CanAccessModule(string moduleName)
    {
        var role = TstAccessService.GetRoleByModuleName(moduleName);
        return role != null && role.CanRead;
    }

    private void ShowBubbles(string toolId)
    {
        hoveredTool = toolId;
        StateHasChanged();
    }

    private void HideBubbles(string toolId)
    {
        hoveredTool = null;
        StateHasChanged();
    }

    private int GetModuleCount(TstToolModel tool)
    {
        return tool.Modules?.Where(m => CanAccessModule(m.Name)).Count() ?? 0;
    }

    public void Dispose()
    {
        // Méthode requise par IDisposable
    }

    private string GetModuleIcon(string moduleName)  
    {  
       return moduleName switch  
              {  
                 "service-inclusion-exclusion" => "bi bi-sliders",  
                 "config-ini" => "bi bi-gear",  
                 "appsettings-plateforms" => "bi bi-layers",  
                 "translations-terms" => "bi bi-translate",  
                 "role-management" => "bi bi-people",  
                 "gestion-webtracing" => "bi bi-graph-up",  
                 "modifier-temps-panier" => "bi bi-clock",  
                 "gestion-coupons-promo" => "bi bi-tags",  
                 "transfere-pointage-photo" => "bi bi-camera",  
                 "gestion-maquette-abo-fermer" => "bi bi-box-arrow-in-down",  
                 "preparation-mise-vente" => "bi bi-shop",  
                 "partners" => "bi bi-handshake",  
                 "widget-waiting-list" => "bi bi-list-check",  
                 "widget-catalogue-offre" => "bi bi-card-list",  
                 _ => "bi bi-question-circle"  
              };  
    }
}