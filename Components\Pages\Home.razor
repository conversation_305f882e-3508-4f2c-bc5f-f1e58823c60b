@page "/"
@attribute [Authorize]
@attribute [StreamRendering]
@rendermode @(new InteractiveServerRenderMode(false))
@inject ITstAccessService TstAccessService
@inject IStringLocalizer<Resource> Localizer
@inject NavigationManager NavigationManager
@implements IDisposable

<PageTitle>Home</PageTitle>

<!-- Section des outils -->
<div class="container my-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h2 class="display-6 fw-bold text-primary mb-3">Outils de Gestion</h2>
            <p class="text-muted fs-5">@Localizer["home_outil_gesion_tst"]</p>
        </div>
    </div>

    <div class="row g-4">
        @foreach (var tool in Tools)
        {
            if (CanAccessTool(tool.Id))
            {
                <div class="col-lg-4 col-md-6">
                    <div class="modern-tool-card @(selectedTool == tool.Id ? "expanded" : "")"
                         @onclick="() => ToggleModules(tool.Id)">

                        <!-- Card principale de l'outil -->
                        <div class="tool-header">
                            <div class="tool-icon-container" style="background: linear-gradient(135deg, @tool.Color, @GetDarkerColor(tool.Color));">
                                <i class="@tool.Icon"></i>
                            </div>
                            <div class="tool-content">
                                <h5 class="tool-title">@Localizer[tool.Name]</h5>
                                <p class="tool-description">@Localizer[tool.Description]</p>
                                <div class="tool-action">
                                    <span class="action-text">@GetModuleCount(tool) modules</span>
                                    <i class="bi bi-chevron-down expand-icon @(selectedTool == tool.Id ? "rotated" : "")"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Modules expandables -->
                        <div class="modules-container @(selectedTool == tool.Id ? "show" : "")">
                            <div class="modules-grid">
                                @foreach (var module in tool.Modules.Where(m => CanAccessModule(m.Name)))
                                {
                                    <a href="@module.Name" class="module-item">
                                        <div class="module-icon">
                                            <i class="@GetModuleIcon(module.Name)"></i>
                                        </div>
                                        <span class="module-name">@Localizer[module.Name]</span>
                                        <i class="bi bi-arrow-right module-arrow"></i>
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
    </div>
</div>

<!-- Section statistiques -->
<div class="bg-light py-5 mt-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-primary">24/7</h3>
                    <p class="text-muted mb-0">@Localizer["stats_support_continu"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-success">+50%</h3>
                    <p class="text-muted mb-0">@Localizer["stats_productivite"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-info">100%</h3>
                    <p class="text-muted mb-0">@Localizer["stats_optimise"]</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="p-3">
                    <h3 class="display-4 fw-bold text-warning">∞</h3>
                    <p class="text-muted mb-0">@Localizer["stats_possibilites"]</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Section CTA -->
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 text-center">
            <h3 class="fw-bold mb-3">@Localizer["home_worflow_questions"]</h3>
            <p class="text-muted mb-4">@Localizer["home_explorer_fonctionnalite"]</p>
            <div class="d-flex justify-content-center gap-3">
                <i class="bi bi-arrow-up text-primary fs-3"></i>
                <span class="text-muted">@Localizer["common_survolez_outils_pour_voir_modules"]</span>
            </div>
        </div>
    </div>
</div>

<style>
    .modern-tool-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        overflow: hidden;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .modern-tool-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    }

    .modern-tool-card.expanded {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    .tool-header {
        padding: 24px;
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .tool-icon-container {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .tool-icon-container i {
        font-size: 28px;
        color: white;
    }

    .tool-content {
        flex: 1;
        min-width: 0;
    }

    .tool-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 4px 0;
        color: #1a1a1a;
    }

    .tool-description {
        font-size: 14px;
        color: #6b7280;
        margin: 0 0 12px 0;
        line-height: 1.4;
    }

    .tool-action {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .action-text {
        font-size: 13px;
        color: #3b82f6;
        font-weight: 500;
    }

    .expand-icon {
        font-size: 16px;
        color: #6b7280;
        transition: transform 0.3s ease;
    }

    .expand-icon.rotated {
        transform: rotate(180deg);
    }

    .modules-container {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: #f8fafc;
        border-top: 1px solid #e5e7eb;
    }

    .modules-container.show {
        max-height: 400px;
    }

    .modules-grid {
        padding: 16px 24px 24px;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .module-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background: white;
        border-radius: 12px;
        text-decoration: none;
        color: #374151;
        transition: all 0.2s ease;
        border: 1px solid #e5e7eb;
        gap: 12px;
    }

    .module-item:hover {
        background: #f3f4f6;
        transform: translateX(4px);
        color: #1f2937;
        text-decoration: none;
        border-color: #d1d5db;
    }

    .module-icon {
        width: 32px;
        height: 32px;
        background: #f3f4f6;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .module-icon i {
        font-size: 16px;
        color: #3b82f6;
    }

    .module-name {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
    }

    .module-arrow {
        font-size: 14px;
        color: #9ca3af;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .module-item:hover .module-arrow {
        opacity: 1;
    }

    /* Animation pour l'apparition des modules */
    .modules-grid .module-item {
        opacity: 0;
        transform: translateY(10px);
        animation: slideInUp 0.3s ease forwards;
    }

    .modules-container.show .module-item:nth-child(1) { animation-delay: 0.1s; }
    .modules-container.show .module-item:nth-child(2) { animation-delay: 0.15s; }
    .modules-container.show .module-item:nth-child(3) { animation-delay: 0.2s; }
    .modules-container.show .module-item:nth-child(4) { animation-delay: 0.25s; }
    .modules-container.show .module-item:nth-child(5) { animation-delay: 0.3s; }
</style>

<!-- CSS avec keyframes séparé -->
<style>
    @@keyframes slideInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

@code {
    private List<TstToolModel> Tools { get; set; } = new();
    private string? selectedTool = null;

    protected override void OnInitialized()
    {
        Tools = TstAccessService.GetTools();
    }

    private bool CanAccessTool(string toolId)
    {
        return TstAccessService.CanAccessTool(toolId);
    }

    private bool CanAccessModule(string moduleName)
    {
        var role = TstAccessService.GetRoleByModuleName(moduleName);
        return role != null && role.CanRead;
    }

    private void ToggleModules(string toolId)
    {
        if (selectedTool == toolId)
        {
            selectedTool = null;
        }
        else
        {
            selectedTool = toolId;
        }
        StateHasChanged();
    }

    private string GetDarkerColor(string color)
    {
        // Fonction pour assombrir une couleur pour le gradient
        if (string.IsNullOrEmpty(color)) return "#333333";

        // Si c'est un nom de couleur CSS, retourner une version plus sombre
        return color switch
        {
            var c when c.Contains("#") => DarkenHexColor(c),
            "red" => "#cc0000",
            "blue" => "#0066cc",
            "green" => "#006600",
            "orange" => "#cc6600",
            "purple" => "#6600cc",
            "teal" => "#006666",
            "pink" => "#cc0066",
            "yellow" => "#cccc00",
            _ => "#333333"
        };
    }

    private string DarkenHexColor(string hex)
    {
        if (hex.Length != 7 || !hex.StartsWith("#")) return "#333333";

        try
        {
            var r = Convert.ToInt32(hex.Substring(1, 2), 16);
            var g = Convert.ToInt32(hex.Substring(3, 2), 16);
            var b = Convert.ToInt32(hex.Substring(5, 2), 16);

            r = Math.Max(0, r - 40);
            g = Math.Max(0, g - 40);
            b = Math.Max(0, b - 40);

            return $"#{r:X2}{g:X2}{b:X2}";
        }
        catch
        {
            return "#333333";
        }
    }

    private int GetModuleCount(TstToolModel tool)
    {
        return tool.Modules?.Where(m => CanAccessModule(m.Name)).Count() ?? 0;
    }

    public void Dispose()
    {
        // Méthode requise par IDisposable - pas de ressources à libérer dans ce nouveau design
    }

    private string GetModuleIcon(string moduleName)  
    {  
       return moduleName switch  
              {  
                 "service-inclusion-exclusion" => "bi bi-sliders",  
                 "config-ini" => "bi bi-gear",  
                 "appsettings-plateforms" => "bi bi-layers",  
                 "translations-terms" => "bi bi-translate",  
                 "role-management" => "bi bi-people",  
                 "gestion-webtracing" => "bi bi-graph-up",  
                 "modifier-temps-panier" => "bi bi-clock",  
                 "gestion-coupons-promo" => "bi bi-tags",  
                 "transfere-pointage-photo" => "bi bi-camera",  
                 "gestion-maquette-abo-fermer" => "bi bi-box-arrow-in-down",  
                 "preparation-mise-vente" => "bi bi-shop",  
                 "partners" => "bi bi-handshake",  
                 "widget-waiting-list" => "bi bi-list-check",  
                 "widget-catalogue-offre" => "bi bi-card-list",  
                 _ => "bi bi-question-circle"  
              };  
    }
}