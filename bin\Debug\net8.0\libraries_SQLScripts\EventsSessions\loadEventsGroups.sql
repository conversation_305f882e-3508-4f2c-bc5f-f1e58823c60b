
/*
declare @pLangCode varchar(max)
set @pLangCode='fr'
*/

declare @langCode varchar(max)
set @langCode=@plangCode


DECLARE @langue_id int = (select langue_id from langue where langue_code = @langCode)




select *
from manifestation_groupe mg
LEFT JOIN traduction_manifestation_groupe trad_manif_group on mg.manifestation_id = trad_manif_group.manifestation_id 
		and trad_manif_group.langue_id = @langue_id 
where supprimer <> 'O' 



/*
select distinct
isnull(trad_manif_group.manif_groupe_id, mg.manif_groupe_id) as manif_groupe_id,
isnull(trad_manif_group.manif_groupe_nom, mg.manif_groupe_nom) as manif_groupe_nom,
isnull(trad_manif_group.manif_groupe_code, mg.manif_groupe_code) as manif_groupe_code
from manifestation_groupe mg
LEFT JOIN traduction_manifestation_groupe trad_manif_group on mg.manifestation_id = trad_manif_group.manifestation_id and trad_manif_group.langue_id = @langue_id
where supprimer <> 'O'

*/